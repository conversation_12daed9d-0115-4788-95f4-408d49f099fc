const jwt = require('jsonwebtoken');

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_here_change_in_production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

/**
 * 生成JWT token
 * @param {object} payload - 要编码的用户信息
 * @returns {string} JWT token
 */
function generateToken(payload) {
  try {
    // 确保payload包含必要的用户信息
    const tokenPayload = {
      user_id: payload.user_id,
      username: payload.username,
      email: payload.email,
      role: payload.role || 1,
      status: payload.status || 1,
      iat: Math.floor(Date.now() / 1000) // 签发时间
    };
    
    const token = jwt.sign(tokenPayload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'learning-happy-paradise-api',
      audience: 'learning-happy-paradise-client'
    });
    
    return token;
  } catch (error) {
    console.error('JWT生成失败:', error);
    throw new Error('Token生成失败');
  }
}

/**
 * 验证JWT token
 * @param {string} token - JWT token
 * @returns {object} 解码后的用户信息
 */
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'learning-happy-paradise-api',
      audience: 'learning-happy-paradise-client'
    });
    
    return decoded;
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token已过期');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Token无效');
    } else if (error.name === 'NotBeforeError') {
      throw new Error('Token尚未生效');
    } else {
      console.error('JWT验证失败:', error);
      throw new Error('Token验证失败');
    }
  }
}

/**
 * 解码JWT token（不验证签名）
 * @param {string} token - JWT token
 * @returns {object} 解码后的信息
 */
function decodeToken(token) {
  try {
    return jwt.decode(token);
  } catch (error) {
    console.error('JWT解码失败:', error);
    throw new Error('Token解码失败');
  }
}

/**
 * 刷新JWT token
 * @param {string} token - 旧的JWT token
 * @returns {string} 新的JWT token
 */
function refreshToken(token) {
  try {
    const decoded = verifyToken(token);
    
    // 移除时间相关的字段，重新生成token
    const { iat, exp, ...payload } = decoded;
    
    return generateToken(payload);
  } catch (error) {
    throw error;
  }
}

/**
 * 获取token过期时间
 * @param {string} token - JWT token
 * @returns {Date} 过期时间
 */
function getTokenExpiration(token) {
  try {
    const decoded = decodeToken(token);
    return new Date(decoded.exp * 1000);
  } catch (error) {
    throw error;
  }
}

/**
 * 检查token是否即将过期（默认30分钟内）
 * @param {string} token - JWT token
 * @param {number} minutesBeforeExpiry - 提前多少分钟算作即将过期
 * @returns {boolean} 是否即将过期
 */
function isTokenExpiringSoon(token, minutesBeforeExpiry = 30) {
  try {
    const expirationTime = getTokenExpiration(token);
    const now = new Date();
    const timeDiff = expirationTime.getTime() - now.getTime();
    const minutesDiff = timeDiff / (1000 * 60);
    
    return minutesDiff <= minutesBeforeExpiry;
  } catch (error) {
    return true; // 如果无法解析，认为需要刷新
  }
}

/**
 * 从请求头中提取token
 * @param {object} req - Express请求对象
 * @returns {string|null} JWT token或null
 */
function extractTokenFromRequest(req) {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7); // 移除 'Bearer ' 前缀
  }
  
  // 也可以从查询参数中获取token（不推荐，仅用于特殊情况）
  if (req.query.token) {
    return req.query.token;
  }
  
  return null;
}

module.exports = {
  generateToken,
  verifyToken,
  decodeToken,
  refreshToken,
  getTokenExpiration,
  isTokenExpiringSoon,
  extractTokenFromRequest,
  JWT_SECRET,
  JWT_EXPIRES_IN
};
