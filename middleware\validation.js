/**
 * 用户注册数据验证中间件
 */

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证手机号格式（中国大陆）
 * @param {string} mobile - 手机号
 * @returns {boolean} 是否为有效手机号
 */
function isValidMobile(mobile) {
  const mobileRegex = /^1[3-9]\d{9}$/;
  return mobileRegex.test(mobile);
}

/**
 * 验证用户名格式
 * @param {string} username - 用户名
 * @returns {boolean} 是否为有效用户名
 */
function isValidUsername(username) {
  // 用户名：3-20位，只能包含字母、数字、下划线，不能以数字开头
  const usernameRegex = /^[a-zA-Z_][a-zA-Z0-9_]{2,19}$/;
  return usernameRegex.test(username);
}

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {object} 验证结果
 */
function validatePassword(password) {
  const result = {
    isValid: true,
    errors: []
  };

  // 密码长度检查
  if (!password || password.length < 6) {
    result.isValid = false;
    result.errors.push('密码长度不能少于6位');
  }

  if (password && password.length > 50) {
    result.isValid = false;
    result.errors.push('密码长度不能超过50位');
  }

  // 密码复杂度检查（至少包含字母和数字）
  if (password && !/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含至少一个字母和一个数字');
  }

  return result;
}

/**
 * 验证性别值
 * @param {number} gender - 性别值
 * @returns {boolean} 是否为有效性别值
 */
function isValidGender(gender) {
  return [0, 1, 2].includes(parseInt(gender));
}

/**
 * 验证生日格式
 * @param {string} birthday - 生日（YYYY-MM-DD格式）
 * @returns {boolean} 是否为有效生日
 */
function isValidBirthday(birthday) {
  if (!birthday) return true; // 生日是可选的
  
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(birthday)) return false;
  
  const date = new Date(birthday);
  const now = new Date();
  
  // 检查日期是否有效且不能是未来日期
  return date instanceof Date && 
         !isNaN(date) && 
         date <= now &&
         date.getFullYear() >= 1900;
}

/**
 * 用户注册数据验证中间件
 */
function validateUserRegistration(req, res, next) {
  const { username, email, mobile, password, nickname, gender, birthday, bio } = req.body;
  const errors = [];

  // 必填字段检查
  if (!username) {
    errors.push('用户名不能为空');
  } else if (!isValidUsername(username)) {
    errors.push('用户名格式不正确（3-20位，只能包含字母、数字、下划线，不能以数字开头）');
  }

  if (!email) {
    errors.push('邮箱不能为空');
  } else if (!isValidEmail(email)) {
    errors.push('邮箱格式不正确');
  }

  if (!password) {
    errors.push('密码不能为空');
  } else {
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      errors.push(...passwordValidation.errors);
    }
  }

  // 可选字段检查
  if (mobile && !isValidMobile(mobile)) {
    errors.push('手机号格式不正确');
  }

  if (nickname && (nickname.length < 1 || nickname.length > 50)) {
    errors.push('昵称长度应在1-50个字符之间');
  }

  if (gender !== undefined && !isValidGender(gender)) {
    errors.push('性别值不正确（0-未知，1-男，2-女）');
  }

  if (birthday && !isValidBirthday(birthday)) {
    errors.push('生日格式不正确或日期无效');
  }

  if (bio && bio.length > 200) {
    errors.push('个人简介不能超过200个字符');
  }

  // 如果有验证错误，返回错误信息
  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors: errors
    });
  }

  // 验证通过，继续执行下一个中间件
  next();
}

/**
 * 通用参数验证中间件
 * @param {object} rules - 验证规则
 */
function validateParams(rules) {
  return (req, res, next) => {
    const errors = [];
    
    for (const [field, rule] of Object.entries(rules)) {
      const value = req.body[field];
      
      if (rule.required && (!value || value.toString().trim() === '')) {
        errors.push(`${rule.name || field}不能为空`);
        continue;
      }
      
      if (value && rule.type) {
        switch (rule.type) {
          case 'email':
            if (!isValidEmail(value)) {
              errors.push(`${rule.name || field}格式不正确`);
            }
            break;
          case 'mobile':
            if (!isValidMobile(value)) {
              errors.push(`${rule.name || field}格式不正确`);
            }
            break;
          case 'username':
            if (!isValidUsername(value)) {
              errors.push(`${rule.name || field}格式不正确`);
            }
            break;
        }
      }
      
      if (value && rule.minLength && value.length < rule.minLength) {
        errors.push(`${rule.name || field}长度不能少于${rule.minLength}位`);
      }
      
      if (value && rule.maxLength && value.length > rule.maxLength) {
        errors.push(`${rule.name || field}长度不能超过${rule.maxLength}位`);
      }
    }
    
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors
      });
    }
    
    next();
  };
}

module.exports = {
  validateUserRegistration,
  validateParams,
  isValidEmail,
  isValidMobile,
  isValidUsername,
  validatePassword,
  isValidGender,
  isValidBirthday
};
