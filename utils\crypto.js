const crypto = require('crypto');

/**
 * 生成随机盐值
 * @param {number} length - 盐值长度，默认32字节
 * @returns {string} 十六进制格式的盐值
 */
function generateSalt(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 使用PBKDF2算法加密密码
 * @param {string} password - 原始密码
 * @param {string} salt - 盐值
 * @param {number} iterations - 迭代次数，默认10000
 * @param {number} keyLength - 密钥长度，默认64字节
 * @returns {string} 十六进制格式的加密密码
 */
function hashPassword(password, salt, iterations = 10000, keyLength = 64) {
  return crypto.pbkdf2Sync(password, salt, iterations, keyLength, 'sha256').toString('hex');
}

/**
 * 验证密码是否正确
 * @param {string} password - 用户输入的密码
 * @param {string} salt - 存储的盐值
 * @param {string} hashedPassword - 存储的加密密码
 * @param {number} iterations - 迭代次数，默认10000
 * @param {number} keyLength - 密钥长度，默认64字节
 * @returns {boolean} 密码是否正确
 */
function verifyPassword(password, salt, hashedPassword, iterations = 10000, keyLength = 64) {
  const hash = hashPassword(password, salt, iterations, keyLength);
  return hash === hashedPassword;
}

/**
 * 生成加密密码和盐值
 * @param {string} password - 原始密码
 * @returns {object} 包含salt和hashedPassword的对象
 */
function createPasswordHash(password) {
  const salt = generateSalt();
  const hashedPassword = hashPassword(password, salt);
  
  return {
    salt,
    hashedPassword
  };
}

/**
 * 生成随机验证码
 * @param {number} length - 验证码长度，默认6位
 * @returns {string} 数字验证码
 */
function generateVerificationCode(length = 6) {
  const digits = '0123456789';
  let code = '';
  
  for (let i = 0; i < length; i++) {
    code += digits[Math.floor(Math.random() * digits.length)];
  }
  
  return code;
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
function generateUUID() {
  return crypto.randomUUID();
}

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @param {string} charset - 字符集，默认包含字母和数字
 * @returns {string} 随机字符串
 */
function generateRandomString(length = 32, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
}

module.exports = {
  generateSalt,
  hashPassword,
  verifyPassword,
  createPasswordHash,
  generateVerificationCode,
  generateUUID,
  generateRandomString
};
