const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { createPasswordHash, generateVerificationCode } = require('../utils/crypto');
const { validateUserRegistration } = require('../middleware/validation');

/**
 * 获取客户端IP地址
 * @param {object} req - Express请求对象
 * @returns {string} IP地址
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         '127.0.0.1';
}

/**
 * 检查用户名是否已存在
 * @param {string} username - 用户名
 * @returns {boolean} 是否存在
 */
async function checkUsernameExists(username) {
  try {
    const result = await db.query('SELECT user_id FROM users WHERE username = ?', [username]);
    return result.length > 0;
  } catch (error) {
    console.error('检查用户名是否存在时出错:', error);
    throw error;
  }
}

/**
 * 检查邮箱是否已存在
 * @param {string} email - 邮箱
 * @returns {boolean} 是否存在
 */
async function checkEmailExists(email) {
  try {
    const result = await db.query('SELECT user_id FROM users WHERE email = ?', [email]);
    return result.length > 0;
  } catch (error) {
    console.error('检查邮箱是否存在时出错:', error);
    throw error;
  }
}

/**
 * 检查手机号是否已存在
 * @param {string} mobile - 手机号
 * @returns {boolean} 是否存在
 */
async function checkMobileExists(mobile) {
  try {
    if (!mobile) return false;
    const result = await db.query('SELECT user_id FROM users WHERE mobile = ?', [mobile]);
    return result.length > 0;
  } catch (error) {
    console.error('检查手机号是否存在时出错:', error);
    throw error;
  }
}

/**
 * 用户注册接口
 * POST /api/users/register
 */
router.post('/register', validateUserRegistration, async (req, res) => {
  try {
    const {
      username,
      email,
      mobile,
      password,
      nickname,
      bio,
      gender = 0,
      birthday
    } = req.body;

    // 暂时跳过数据库检查，先测试数据验证功能
    console.log('📝 收到注册请求:', { username, email, mobile });

    // 生成密码哈希和盐值
    const { salt, hashedPassword } = createPasswordHash(password);

    // 生成验证码
    const verificationCode = generateVerificationCode();

    // 获取客户端IP
    const registerIP = getClientIP(req);

    // 模拟成功注册的响应
    const newUser = {
      user_id: Math.floor(Math.random() * 10000), // 模拟ID
      username,
      email,
      mobile: mobile || null,
      nickname: nickname || username,
      bio: bio || null,
      gender: parseInt(gender),
      birthday: birthday || null,
      status: 2,
      register_time: new Date().toISOString(),
      role: 1,
      is_verified: 0,
      register_ip: registerIP
    };

    console.log(`✅ 用户注册成功（模拟）: ${username}`);

    res.status(201).json({
      success: true,
      message: '注册成功（当前为测试模式，未连接数据库）',
      data: {
        user: newUser,
        verification_code: verificationCode,
        password_info: {
          salt: salt.substring(0, 10) + '...',
          hash: hashedPassword.substring(0, 10) + '...'
        }
      }
    });

  } catch (error) {
    console.error('用户注册错误:', error);

    res.status(500).json({
      success: false,
      message: '服务器内部错误，注册失败',
      code: 'INTERNAL_ERROR',
      error: error.message
    });
  }
});

/**
 * 获取用户信息接口（示例）
 * GET /api/users/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    
    const result = await db.query(`
      SELECT user_id, username, email, mobile, nickname, bio, gender, 
             birthday, status, register_time, last_login_time, role, is_verified
      FROM users 
      WHERE user_id = ?
    `, [userId]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      data: {
        user: result[0]
      }
    });
    
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
