# API 接口文档

## 基础信息

- **服务地址**: http://localhost:4001
- **API前缀**: /api
- **数据格式**: JSON
- **字符编码**: UTF-8

## 用户相关接口

### 1. 用户注册

**接口地址**: `POST /api/users/register`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| username | string | 是 | 用户名（3-20位，字母、数字、下划线，不能以数字开头） | "testuser001" |
| email | string | 是 | 邮箱地址 | "<EMAIL>" |
| password | string | 是 | 密码（至少6位，包含字母和数字） | "test123456" |
| mobile | string | 否 | 手机号（中国大陆格式） | "***********" |
| nickname | string | 否 | 昵称（1-50字符） | "测试用户" |
| bio | string | 否 | 个人简介（最多200字符） | "这是个人简介" |
| gender | number | 否 | 性别（0-未知，1-男，2-女） | 1 |
| birthday | string | 否 | 生日（YYYY-MM-DD格式） | "1990-01-01" |

**请求示例**:
```json
{
  "username": "testuser001",
  "email": "<EMAIL>",
  "mobile": "***********",
  "password": "test123456",
  "nickname": "Test User",
  "bio": "This is a test user bio",
  "gender": 1,
  "birthday": "1990-01-01"
}
```

**成功响应** (HTTP 201):
```json
{
  "success": true,
  "message": "注册成功，请查收邮件进行账号激活",
  "data": {
    "user": {
      "user_id": 2227,
      "username": "testuser001",
      "email": "<EMAIL>",
      "mobile": "***********",
      "nickname": "Test User",
      "bio": "This is a test user bio",
      "gender": 1,
      "birthday": "1990-01-01",
      "status": 2,
      "register_time": "2025-07-17T15:10:13.123Z",
      "role": 1,
      "is_verified": 0,
      "register_ip": "127.0.0.1"
    },
    "verification_code": "123456",
    "password_info": {
      "salt": "a1b2c3d4e5...",
      "hash": "f6g7h8i9j0..."
    }
  }
}
```

**错误响应** (HTTP 400):
```json
{
  "success": false,
  "message": "数据验证失败",
  "errors": [
    "用户名格式不正确（3-20位，只能包含字母、数字、下划线，不能以数字开头）",
    "邮箱格式不正确",
    "密码长度不能少于6位",
    "密码必须包含至少一个字母和一个数字"
  ]
}
```

### 2. 用户登录

**接口地址**: `POST /api/users/login`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| login | string | 是 | 登录标识（用户名或邮箱） | "testuser001" 或 "<EMAIL>" |
| password | string | 是 | 密码 | "test123456" |

**请求示例**:
```json
{
  "login": "testuser001",
  "password": "test123456"
}
```

**成功响应** (HTTP 200):
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "user_id": 1,
      "username": "testuser001",
      "email": "<EMAIL>",
      "mobile": "***********",
      "nickname": "Test User",
      "bio": "This is a test user bio",
      "gender": 1,
      "birthday": "1990-01-01",
      "status": 1,
      "role": 1,
      "is_verified": 0,
      "last_login_time": "2025-07-17T15:42:41.123Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": "24h"
  }
}
```

**错误响应** (HTTP 401):
```json
{
  "success": false,
  "message": "用户名/邮箱或密码错误",
  "code": "INVALID_CREDENTIALS"
}
```

**错误响应** (HTTP 403 - 账号状态异常):
```json
{
  "success": false,
  "message": "账号未激活，请先激活账号",
  "code": "ACCOUNT_STATUS_ERROR"
}
```

### 3. 获取用户信息

**接口地址**: `GET /api/users/:id`

**路径参数**:
- `id`: 用户ID

**成功响应** (HTTP 200):
```json
{
  "success": true,
  "data": {
    "user": {
      "user_id": 1,
      "username": "testuser001",
      "email": "<EMAIL>",
      "mobile": "***********",
      "nickname": "Test User",
      "bio": "This is a test user bio",
      "gender": 1,
      "birthday": "1990-01-01",
      "status": 1,
      "register_time": "2025-07-17T15:10:13.123Z",
      "last_login_time": null,
      "role": 1,
      "is_verified": 0
    }
  }
}
```

## 圈子相关接口

### 1. 获取圈子列表

**接口地址**: `POST /api/circles/list`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| page | number | 否 | 页码（默认1） | 1 |
| pageSize | number | 否 | 每页数量（默认10，最大100） | 10 |
| category_id | number | 否 | 圈子分类ID | 1 |
| type | number | 否 | 圈子类型（1-公开，2-私密） | 1 |
| status | number | 否 | 状态（0-禁用，1-正常，2-审核中，3-已删除，默认1） | 1 |
| author_id | number | 否 | 作者ID | 1 |
| keyword | string | 否 | 关键词搜索（搜索名称、简介、详细介绍） | "技术" |
| sort_by | string | 否 | 排序字段（create_time, update_time, member_count, post_count, name，默认create_time） | "create_time" |
| sort_order | string | 否 | 排序方向（asc, desc，默认desc） | "desc" |

**请求示例**:
```json
{
  "page": 1,
  "pageSize": 10,
  "category_id": 1,
  "type": 1,
  "status": 1,
  "keyword": "技术",
  "sort_by": "member_count",
  "sort_order": "desc"
}
```

**成功响应** (HTTP 200):
```json
{
  "success": true,
  "message": "获取圈子列表成功",
  "data": {
    "circles": [
      {
        "id": 13,
        "category_id": 1,
        "category_name": "技术交流",
        "type": 1,
        "type_name": "公开",
        "name": "Node.js开发者社区",
        "cover": "https://example.com/covers/nodejs.jpg",
        "introduction": "专注于Node.js技术分享与交流的圈子",
        "content_description": "Node.js是一个基于Chrome V8引擎的JavaScript运行时环境...",
        "price": 0.00,
        "annual_fee_type": 1,
        "annual_fee_type_name": "免费",
        "create_time": "2025-07-17T15:30:00.000Z",
        "update_time": "2025-07-17T15:30:00.000Z",
        "status": 1,
        "status_name": "正常",
        "author_id": 1,
        "author_name": "testuser001",
        "member_count": 156,
        "post_count": 89
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 10,
      "total_count": 5,
      "total_pages": 1,
      "has_next_page": false,
      "has_prev_page": false
    },
    "filters": {
      "category_id": 1,
      "type": 1,
      "status": 1,
      "author_id": null,
      "keyword": "技术",
      "sort_by": "member_count",
      "sort_order": "desc"
    }
  }
}
```

### 2. 获取圈子详情

**接口地址**: `GET /api/circles/:id`

**路径参数**:
- `id`: 圈子ID

**成功响应** (HTTP 200):
```json
{
  "success": true,
  "data": {
    "circle": {
      "id": 13,
      "category_id": 1,
      "category_name": "技术交流",
      "type": 1,
      "type_name": "公开",
      "name": "Node.js开发者社区",
      "cover": "https://example.com/covers/nodejs.jpg",
      "introduction": "专注于Node.js技术分享与交流的圈子",
      "content_description": "Node.js是一个基于Chrome V8引擎的JavaScript运行时环境...",
      "price": 0.00,
      "annual_fee_type": 1,
      "annual_fee_type_name": "免费",
      "create_time": "2025-07-17T15:30:00.000Z",
      "update_time": "2025-07-17T15:30:00.000Z",
      "status": 1,
      "status_name": "正常",
      "author_id": 1,
      "author_name": "testuser001",
      "member_count": 156,
      "post_count": 89
    }
  }
}
```

## 系统接口

### 1. 服务状态

**接口地址**: `GET /`

**响应示例**:
```json
{
  "message": "欢迎使用知识乐园后台API服务",
  "version": "1.0.0",
  "status": "running"
}
```

### 2. 健康检查

**接口地址**: `GET /health`

**响应示例**:
```json
{
  "status": "OK",
  "timestamp": "2025-07-17T15:10:13.454Z",
  "uptime": 19.7268269
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| USERNAME_EXISTS | 用户名已存在 |
| EMAIL_EXISTS | 邮箱已被注册 |
| MOBILE_EXISTS | 手机号已被注册 |
| DUPLICATE_ENTRY | 重复条目（用户名、邮箱或手机号已存在） |
| INTERNAL_ERROR | 服务器内部错误 |

## 数据库表结构

### users 表

用户注册接口基于以下数据库表结构设计：

```sql
CREATE TABLE users (
  user_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  username varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  email varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '电子邮箱',
  mobile varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  password_hash varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '加密后的密码',
  salt varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码加密盐值',
  avatar_url varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  nickname varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
  bio varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人简介',
  gender tinyint(4) DEFAULT '0' COMMENT '性别：0-未知 1-男 2-女',
  birthday date DEFAULT NULL COMMENT '生日',
  status tinyint(4) NOT NULL DEFAULT '1' COMMENT '账号状态：0-禁用 1-正常 2-未激活',
  last_login_time datetime DEFAULT NULL COMMENT '最后登录时间',
  last_login_ip varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  register_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  register_ip varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册IP',
  update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_verified tinyint(4) DEFAULT '0' COMMENT '是否认证：0-未认证 1-已认证',
  verification_code varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱/手机验证码',
  verification_expire datetime DEFAULT NULL COMMENT '验证码过期时间',
  role tinyint(4) DEFAULT '1' COMMENT '用户角色：1-普通用户 2-内容创作者 3-管理员',
  PRIMARY KEY (user_id),
  UNIQUE KEY idx_username (username),
  UNIQUE KEY idx_email (email),
  KEY idx_mobile (mobile),
  KEY idx_status (status),
  KEY idx_register_time (register_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

## 注意事项

1. **数据库连接**: 项目已连接到ashuo数据库，所有操作都是真实的数据库操作
2. **密码安全**: 使用PBKDF2算法进行密码加密，包含随机盐值
3. **数据验证**: 所有输入数据都经过严格的格式验证
4. **重复检查**: 自动检查用户名、邮箱、手机号是否已存在
5. **IP记录**: 自动记录用户注册时的IP地址
6. **验证码**: 自动生成6位数字验证码（24小时有效期）
